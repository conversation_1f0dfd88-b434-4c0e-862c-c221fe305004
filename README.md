# Nashira - From Daru to Dolce Vita 🍷

Discover the perfect spirits and wines with Nashira's comprehensive alcohol data extraction platform. Search by country, state, city, and alcohol type for complete market intelligence.

## Project Structure

- `flask_backend.py`: Flask API backend that handles alcohol/wine scraping requests
- `abcd.py`: Core Python scraping functionality with alcohol-specific enhancements
- `business_scraper.py`: Caching and utility functions
- `scraper_king/`: Next.js frontend application (Nashira interface)

## Prerequisites

- Python 3.8 or higher
- Node.js 16 or higher
- npm or yarn

## Setup Instructions

### 1. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 2. Install Node.js Dependencies

```bash
cd scraper_king
npm install
```

## Running the Application

### Option 1: Using the Start Script

On Windows, you can use the provided batch file to start both the backend and frontend:

```bash
start.bat
```

### Option 2: Manual Start

#### Start the Flask Backend

```bash
python flask_backend.py
```

The Flask backend will run on http://localhost:5000.

#### Start the Next.js Frontend

```bash
cd scraper_king
npm run dev
```

The Next.js frontend will run on http://localhost:3000.

## Features

🍷 **Alcohol Type Search**: Wine, whiskey, vodka, rum, gin, beer, champagne, brandy, tequila, liqueur
🌍 **Global Coverage**: Search across multiple countries and regions
🏙️ **Location-Based**: Filter by country, state/region, and city
💰 **Price Filtering**: Set price ranges for targeted searches
⭐ **Rating Filters**: Find only highly-rated establishments
🔍 **Real-time Search**: Live scraping with intelligent caching
📱 **Responsive Design**: Works perfectly on desktop and mobile
🤖 **AI-Powered Intelligence**: Advanced machine learning recommendations
🧠 **Sentiment Analysis**: Analyze reviews and descriptions automatically
🏷️ **Smart Classification**: AI-powered alcohol type detection
🔮 **Price Prediction**: Intelligent price range estimation
🎯 **Semantic Search**: Context-aware search matching
📊 **Smart Results**: AI-ranked results with confidence scores

## Using the Application

1. Open your browser and navigate to http://localhost:3000
2. Select your country, state/region, and city
3. Choose your preferred alcohol type
4. Set price range and minimum rating filters
5. Click "Discover Spirits" to start searching
6. View results with complete business information

## API Endpoints

The Flask backend provides the following API endpoints:

- `POST /api/scrape`: Scrape alcohol/wine data
  - Request body: `{ "category": "alcohol type", "location": "city, state", "country": "country name" }`
  - Response: Array of alcohol business data objects

- `GET /api/health`: Health check endpoint
  - Response: `{ "status": "ok", "message": "Nashira Flask backend is running", "tagline": "From Daru to Dolce Vita" }`

## AI-Powered Features (Hugging Face Integration) ✅ IMPLEMENTED

Nashira now includes advanced AI capabilities powered by Hugging Face models:

🤖 **Sentiment Analysis**: Real-time review analysis using `cardiffnlp/twitter-roberta-base-sentiment-latest`
🏷️ **Product Classification**: Automatic alcohol categorization with `facebook/bart-large-mnli`
🔍 **Semantic Search**: Context-aware matching using `sentence-transformers/all-MiniLM-L6-v2`
💰 **Price Prediction**: Intelligent price range estimation based on business data
🎯 **Smart Recommendations**: AI-powered result ranking with confidence scores
📊 **Multi-factor Scoring**: Combines semantic similarity, sentiment, and classification confidence

### AI API Endpoints

- `GET /api/health`: Health check with AI status
- `POST /api/ai/sentiment`: Analyze text sentiment
- `POST /api/ai/classify`: Classify alcohol types
- `POST /api/ai/similarity`: Calculate semantic similarity
- `POST /api/ai/recommend`: Generate smart recommendations

## Technologies Used

- **Backend**: Python, Flask, BeautifulSoup, Selenium, AsyncIO
- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, Shadcn/UI
- **Search**: DuckDuckGo integration with intelligent filtering
- **Caching**: File-based caching system for performance
- **Future AI**: Hugging Face Transformers (optional enhancement)

## Troubleshooting

- If you encounter CORS issues, make sure the Flask backend is running and properly configured
- Check the browser console for any API errors
- Ensure all dependencies are installed correctly
- For slow searches, try reducing the max results number
