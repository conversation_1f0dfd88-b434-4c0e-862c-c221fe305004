/**
 * Flask API Client
 *
 * This module provides functions to interact with the Flask backend API.
 */

// API base URL - adjust this based on your Flask server configuration
const API_BASE_URL = 'http://localhost:5000/api';

/**
 * Interface for business scraper input
 */
export interface BusinessScrapeInput {
  location: string;
  category: string; // Used as a generic search term
  country: string;
  maxResults?: number; // Maximum number of results to scrape
  filterShopify?: boolean; // Filter for Shopify sites
  filterActive?: boolean; // Filter for active domains
}

/**
 * Interface for business scraper result
 */
export interface BusinessScrapeResult {
  name: string;
  address: string;
  phone: string;
  url: string;
  category?: string;
  social_links?: string;
  is_shopify?: boolean;
  is_active?: boolean;
  status?: string;
}

/**
 * Scrape business data using the Flask API
 *
 * @param input - The scrape input parameters
 * @returns Promise with the scraped business data
 */
export async function scrapeBusiness(input: BusinessScrapeInput): Promise<BusinessScrapeResult[]> {
  try {
    const response = await fetch(`${API_BASE_URL}/scrape`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });

    if (!response.ok) {
      try {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to scrape business data');
      } catch (jsonError) {
        // If we can't parse the error as JSON, use the status text
        throw new Error(`Failed to scrape business data: ${response.status} ${response.statusText}`);
      }
    }

    try {
      const data = await response.json();
      return data;
    } catch (jsonError) {
      console.error('Error parsing response JSON:', jsonError);
      throw new Error('Failed to parse response from server');
    }
  } catch (error) {
    console.error('Error scraping business data:', error);
    throw error;
  }
}

/**
 * Upload a CSV file for business scraping with enhanced error handling
 *
 * @param file - The CSV file to upload
 * @param useUploader - Whether to use the csv_uploader.py implementation
 * @returns Promise with the scraped business data
 */
export async function uploadCsv(file: File, useUploader: boolean = false): Promise<BusinessScrapeResult[]> {
  try {
    console.log(`Starting CSV upload: ${file.name}, size: ${file.size}, type: ${file.type}`);
    
    const formData = new FormData();
    formData.append('file', file);

    const endpoint = useUploader ? `${API_BASE_URL}/upload-csv-with-uploader` : `${API_BASE_URL}/upload-csv`;
    console.log(`Uploading CSV to endpoint: ${endpoint}`);

    // Set a longer timeout for the request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout
    
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData,
        mode: 'cors',
        credentials: 'same-origin',
        headers: {
          'Accept': 'application/json',
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      console.log(`Upload response status: ${response.status} ${response.statusText}`);
      
      if (!response.ok) {
        // Try to get detailed error information
        const responseText = await response.text();
        console.error(`Error response body: ${responseText}`);
        
        try {
          const errorData = JSON.parse(responseText);
          const errorMessage = errorData.error || 'Unknown server error';
          const errorDetails = errorData.traceback ? `\n\nDetails: ${errorData.traceback}` : '';
          throw new Error(`${errorMessage}${errorDetails}`);
        } catch (jsonError) {
          // If we can't parse the error as JSON, use the status text
          throw new Error(`Failed to upload CSV file: ${response.status} ${response.statusText}\n\nResponse: ${responseText.substring(0, 500)}`);
        }
      }

      try {
        const data = await response.json();
        console.log(`Upload successful, received ${data.length} results`);
        return data;
      } catch (jsonError) {
        console.error('Error parsing response JSON:', jsonError);
        throw new Error('Failed to parse response from server');
      }
    } catch (fetchError) {
      clearTimeout(timeoutId);

      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        throw new Error('Request timed out after 60 seconds');
      }

      throw fetchError;
    }
  } catch (error) {
    console.error('Error uploading CSV:', error);
    throw error;
  }
}

/**
 * Check if the Flask API is running
 * 
 * @returns Promise with health check information
 */
export async function checkApiHealth(): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    
    if (!response.ok) {
      throw new Error('API health check failed');
    }
    
    return await response.json();
  } catch (error) {
    console.error('API health check error:', error);
    throw error;
  }
}

/**
 * Test CSV upload functionality
 * 
 * @param file - The CSV file to upload
 * @returns Promise with basic information about the file
 */
export async function testUploadCsv(file: File): Promise<any> {
  try {
    console.log(`Testing CSV upload for file: ${file.name}, size: ${file.size}, type: ${file.type}`);
    
    const formData = new FormData();
    formData.append('file', file);

    const endpoint = `${API_BASE_URL}/test-upload`;
    console.log(`Uploading CSV to test endpoint: ${endpoint}`);

    const response = await fetch(endpoint, {
      method: 'POST',
      body: formData,
      mode: 'cors',
      credentials: 'same-origin',
      headers: {
        'Accept': 'application/json',
      },
    });
    
    console.log(`Test upload response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      try {
        const errorText = await response.text();
        console.error("Error response body:", errorText);
        
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.error || `Test upload failed: ${response.status} ${response.statusText}`);
        } catch (jsonError) {
          throw new Error(`Test upload failed: ${response.status} ${response.statusText}. Response: ${errorText.substring(0, 200)}`);
        }
      } catch (responseError) {
        throw new Error(`Test upload failed: ${response.status} ${response.statusText}`);
      }
    }

    const data = await response.json();
    console.log(`Test upload successful:`, data);
    return data;
  } catch (error) {
    console.error('Error in testUploadCsv function:', error);
    throw error;
  }
}

/**
 * Simple CSV upload with minimal processing
 * 
 * @param file - The CSV file to upload
 * @returns Promise with basic information about the file
 */
export async function simpleUploadCsv(file: File): Promise<any> {
  try {
    console.log(`Simple CSV upload for file: ${file.name}, size: ${file.size}, type: ${file.type}`);
    
    const formData = new FormData();
    formData.append('file', file);

    const endpoint = `${API_BASE_URL}/simple-csv-upload`;
    console.log(`Uploading CSV to simple endpoint: ${endpoint}`);

    const response = await fetch(endpoint, {
      method: 'POST',
      body: formData,
      mode: 'cors',
      credentials: 'same-origin',
      headers: {
        'Accept': 'application/json',
      },
    });
    
    console.log(`Simple upload response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error("Error response body:", errorText);
      
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.error || `Simple upload failed: ${response.status} ${response.statusText}`);
      } catch (jsonError) {
        throw new Error(`Simple upload failed: ${response.status} ${response.statusText}. Response: ${errorText.substring(0, 200)}`);
      }
    }

    const data = await response.json();
    console.log(`Simple upload successful:`, data);
    return data;
  } catch (error) {
    console.error('Error in simpleUploadCsv function:', error);
    throw error;
  }
}

/**
 * Standalone CSV upload that doesn't rely on external modules
 * 
 * @param file - The CSV file to upload
 * @returns Promise with the processed data
 */
export async function standaloneUploadCsv(file: File): Promise<any> {
  try {
    console.log(`Standalone CSV upload for file: ${file.name}, size: ${file.size}, type: ${file.type}`);
    
    const formData = new FormData();
    formData.append('file', file);

    const endpoint = `${API_BASE_URL}/standalone-csv-upload`;
    console.log(`Uploading CSV to standalone endpoint: ${endpoint}`);

    const response = await fetch(endpoint, {
      method: 'POST',
      body: formData,
      mode: 'cors',
      headers: {
        'Accept': 'application/json',
      },
    });
    
    console.log(`Standalone upload response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error("Error response body:", errorText);
      
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.error || `Standalone upload failed: ${response.status} ${response.statusText}`);
      } catch (jsonError) {
        throw new Error(`Standalone upload failed: ${response.status} ${response.statusText}. Response: ${errorText.substring(0, 200)}`);
      }
    }

    const data = await response.json();
    console.log(`Standalone upload successful:`, data);
    return data;
  } catch (error) {
    console.error('Error in standaloneUploadCsv function:', error);
    throw error;
  }
}



