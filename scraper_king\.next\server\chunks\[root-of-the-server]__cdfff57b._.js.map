{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/env.js"], "sourcesContent": ["import { createEnv } from \"@t3-oss/env-nextjs\";\nimport { z } from \"zod\";\n\nexport const env = createEnv({\n  /**\n   * Specify your server-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars.\n   */\n  server: {\n    DATABASE_URL: z.string().url(),\n    NODE_ENV: z\n      .enum([\"development\", \"test\", \"production\"])\n      .default(\"development\"),\n  },\n\n  /**\n   * Specify your client-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\n   * `NEXT_PUBLIC_`.\n   */\n  client: {\n    // NEXT_PUBLIC_CLIENTVAR: z.string(),\n  },\n\n  /**\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\n   * middlewares) or client-side so we need to destruct manually.\n   */\n  runtimeEnv: {\n    DATABASE_URL: process.env.DATABASE_URL,\n    NODE_ENV: process.env.NODE_ENV,\n    // NEXT_PUBLIC_CLIENTVAR: process.env.NEXT_PUBLIC_CLIENTVAR,\n  },\n  /**\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\n   * useful for Docker builds.\n   */\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\n  /**\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\n   * `SOME_VAR=''` will throw an error.\n   */\n  emptyStringAsUndefined: true,\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QAC5B,UAAU,sIAAA,CAAA,IAAC,CACR,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa,EAC1C,OAAO,CAAC;IACb;IAEA;;;;GAIC,GACD,QAAQ;IAER;IAEA;;;GAGC,GACD,YAAY;QACV,cAAc,QAAQ,GAAG,CAAC,YAAY;QACtC,QAAQ;IAEV;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/db.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\nimport { env } from \"~/env\";\n\nconst createPrismaClient = () =>\n  new PrismaClient({\n    log:\n      env.NODE_ENV === \"development\" ? [\"query\", \"error\", \"warn\"] : [\"error\"],\n  });\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: ReturnType<typeof createPrismaClient> | undefined;\n};\n\nexport const db = globalForPrisma.prisma ?? createPrismaClient();\n\nif (env.NODE_ENV !== \"production\") globalForPrisma.prisma = db;\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEA,MAAM,qBAAqB,IACzB,IAAI,6HAAA,CAAA,eAAY,CAAC;QACf,KACE,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBAAgB;YAAC;YAAS;YAAS;SAAO,GAAG;YAAC;SAAQ;IAC3E;AAEF,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI;AAE5C,IAAI,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,cAAc,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/api/trpc.ts"], "sourcesContent": ["/**\n * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:\n * 1. You want to modify request context (see Part 1).\n * 2. You want to create a new middleware or type of procedure (see Part 3).\n *\n * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will\n * need to use are documented accordingly near the end.\n */\nimport { initTRPC } from \"@trpc/server\";\nimport superjson from \"superjson\";\nimport { ZodError } from \"zod\";\n\nimport { db } from \"~/server/db\";\n\n/**\n * 1. CONTEXT\n *\n * This section defines the \"contexts\" that are available in the backend API.\n *\n * These allow you to access things when processing a request, like the database, the session, etc.\n *\n * This helper generates the \"internals\" for a tRPC context. The API handler and RSC clients each\n * wrap this and provides the required context.\n *\n * @see https://trpc.io/docs/server/context\n */\nexport const createTRPCContext = async (opts: { headers: Headers }) => {\n  return {\n    db,\n    ...opts,\n  };\n};\n\n/**\n * 2. INITIALIZATION\n *\n * This is where the tRPC API is initialized, connecting the context and transformer. We also parse\n * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation\n * errors on the backend.\n */\nconst t = initTRPC.context<typeof createTRPCContext>().create({\n  transformer: superjson,\n  errorFormatter({ shape, error }) {\n    return {\n      ...shape,\n      data: {\n        ...shape.data,\n        zodError:\n          error.cause instanceof ZodError ? error.cause.flatten() : null,\n      },\n    };\n  },\n});\n\n/**\n * Create a server-side caller.\n *\n * @see https://trpc.io/docs/server/server-side-calls\n */\nexport const createCallerFactory = t.createCallerFactory;\n\n/**\n * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)\n *\n * These are the pieces you use to build your tRPC API. You should import these a lot in the\n * \"/src/server/api/routers\" directory.\n */\n\n/**\n * This is how you create new routers and sub-routers in your tRPC API.\n *\n * @see https://trpc.io/docs/router\n */\nexport const createTRPCRouter = t.router;\n\n/**\n * Middleware for timing procedure execution and adding an artificial delay in development.\n *\n * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating\n * network latency that would occur in production but not in local development.\n */\nconst timingMiddleware = t.middleware(async ({ next, path }) => {\n  const start = Date.now();\n\n  if (t._config.isDev) {\n    // artificial delay in dev\n    const waitMs = Math.floor(Math.random() * 400) + 100;\n    await new Promise((resolve) => setTimeout(resolve, waitMs));\n  }\n\n  const result = await next();\n\n  const end = Date.now();\n  console.log(`[TRPC] ${path} took ${end - start}ms to execute`);\n\n  return result;\n});\n\n/**\n * Public (unauthenticated) procedure\n *\n * This is the base piece you use to build new queries and mutations on your tRPC API. It does not\n * guarantee that a user querying is authorized, but you can still access user session data if they\n * are logged in.\n */\nexport const publicProcedure = t.procedure.use(timingMiddleware);\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;AACD;AAAA;AACA;AACA;AAEA;;;;;AAcO,MAAM,oBAAoB,OAAO;IACtC,OAAO;QACL,IAAA,qHAAA,CAAA,KAAE;QACF,GAAG,IAAI;IACT;AACF;AAEA;;;;;;CAMC,GACD,MAAM,IAAI,kMAAA,CAAA,WAAQ,CAAC,OAAO,GAA6B,MAAM,CAAC;IAC5D,aAAa,4IAAA,CAAA,UAAS;IACtB,gBAAe,EAAE,KAAK,EAAE,KAAK,EAAE;QAC7B,OAAO;YACL,GAAG,KAAK;YACR,MAAM;gBACJ,GAAG,MAAM,IAAI;gBACb,UACE,MAAM,KAAK,YAAY,sIAAA,CAAA,WAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,KAAK;YAC9D;QACF;IACF;AACF;AAOO,MAAM,sBAAsB,EAAE,mBAAmB;AAcjD,MAAM,mBAAmB,EAAE,MAAM;AAExC;;;;;CAKC,GACD,MAAM,mBAAmB,EAAE,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;IACzD,MAAM,QAAQ,KAAK,GAAG;IAEtB,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE;QACnB,0BAA0B;QAC1B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QACjD,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;IACrD;IAEA,MAAM,SAAS,MAAM;IAErB,MAAM,MAAM,KAAK,GAAG;IACpB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,MAAM,EAAE,MAAM,MAAM,aAAa,CAAC;IAE7D,OAAO;AACT;AASO,MAAM,kBAAkB,EAAE,SAAS,CAAC,GAAG,CAAC", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/api/routers/post.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nimport { createTR<PERSON><PERSON>outer, publicProcedure } from \"~/server/api/trpc\";\n\nexport const postRouter = createTRPCRouter({\n  hello: publicProcedure\n    .input(z.object({ text: z.string() }))\n    .query(({ input }) => {\n      return {\n        greeting: `Hello ${input.text}`,\n      };\n    }),\n\n  create: publicProcedure\n    .input(z.object({ name: z.string().min(1) }))\n    .mutation(async ({ ctx, input }) => {\n      return ctx.db.post.create({\n        data: {\n          name: input.name,\n        },\n      });\n    }),\n\n  getLatest: publicProcedure.query(async ({ ctx }) => {\n    const post = await ctx.db.post.findFirst({\n      orderBy: { createdAt: \"desc\" },\n    });\n\n    return post ?? null;\n  }),\n});\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEO,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,OAAO,8HAAA,CAAA,kBAAe,CACnB,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAClC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE;QACf,OAAO;YACL,UAAU,CAAC,MAAM,EAAE,MAAM,IAAI,EAAE;QACjC;IACF;IAEF,QAAQ,8HAAA,CAAA,kBAAe,CACpB,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAAG,IACzC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,OAAO,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,MAAM;gBACJ,MAAM,MAAM,IAAI;YAClB;QACF;IACF;IAEF,WAAW,8HAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QAC7C,MAAM,OAAO,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,OAAO,QAAQ;IACjB;AACF", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/api/routers/scraper.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { createTR<PERSON><PERSON><PERSON>er, publicProcedure } from \"~/server/api/trpc\";\nimport { exec } from \"child_process\";\nimport { promisify } from \"util\";\nimport path from \"path\";\nimport fs from \"fs\";\n\nconst execPromise = promisify(exec);\n\n// Mock function to simulate scraping data from URLs\n// In a real implementation, this would make HTTP requests to the URLs\n// and extract the required information using a library like cheerio\nconst mockScrapeUrl = (url: string) => {\n  // Generate a business name from the URL\n  const domain = url.replace(/^https?:\\/\\//, '').replace(/\\/$/, '').split('/')[0];\n  const domainParts = domain?.split('.') || ['example'];\n  const businessName = domainParts[0] ? domainParts[0].charAt(0).toUpperCase() + domainParts[0].slice(1) : 'Business';\n\n  // Generate a random address\n  const cities = [\"New York\", \"Los Angeles\", \"Chicago\", \"Houston\", \"Phoenix\", \"Philadelphia\"];\n  const randomCity = cities[Math.floor(Math.random() * cities.length)];\n  const randomStreetNumber = Math.floor(Math.random() * 1000) + 1;\n  const streets = [\"Main St\", \"Broadway\", \"Park Ave\", \"Oak St\", \"Maple Ave\", \"Washington Blvd\"];\n  const randomStreet = streets[Math.floor(Math.random() * streets.length)];\n  const address = `${randomStreetNumber} ${randomStreet}, ${randomCity}, NY`;\n\n  // Generate random phone and email\n  const phone = Math.random() > 0.2 ? `+1 (${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}` : '';\n  const email = Math.random() > 0.3 ? `contact@${domain}` : '';\n\n  return {\n    url,\n    name: businessName,\n    address,\n    phone,\n    email\n  };\n};\n\n// Parse CSV content (simple implementation)\nconst parseCsv = (csvContent: string): string[] => {\n  // Split by newlines and filter out empty lines\n  const lines = csvContent.split(/\\r?\\n/).filter(line => line.trim() !== '');\n\n  // Extract URLs (assuming the first column contains URLs)\n  // This is a simplified implementation - a real one would be more robust\n  return lines.map(line => {\n    // Handle quoted values properly\n    if (line.startsWith('\"')) {\n      const match = line.match(/\"([^\"]+)\"/);\n      return match ? match[1] : '';\n    }\n    // Otherwise just take the first column\n    return line.split(',')[0];\n  }).filter(url => url.startsWith('http'));\n};\n\n// Define the schema for the business scraper input\nconst businessScrapeInputSchema = z.object({\n  location: z.string().min(1),\n  category: z.string().min(1), // 'category' is used as a generic search term\n  country: z.string().min(1),\n});\n\n// Define the schema for the business scraper result\nconst businessScrapeResultSchema = z.array(\n  z.object({\n    name: z.string(),\n    address: z.string(),\n    phone: z.string(),\n    email: z.string(),\n    url: z.string(),\n    category: z.string().optional(),\n    social_links: z.string().optional(),\n  })\n);\n\n\n\n// Function to run the Python script for business scraping with optimized performance\nconst runBusinessScraperScript = async (input: z.infer<typeof businessScrapeInputSchema>) => {\n  try {\n    // Get the path to the business_scraper.py script\n    let scriptPath = path.resolve(process.cwd(), \"business_scraper.py\");\n\n    // Check if the script exists\n    if (!fs.existsSync(scriptPath)) {\n      console.error(`Business scraper script not found at ${scriptPath}`);\n      // Try alternative path\n      const altScriptPath = path.resolve(process.cwd(), \"..\", \"business_scraper.py\");\n      if (!fs.existsSync(altScriptPath)) {\n        console.error(`Business scraper script not found at alternative path ${altScriptPath} either`);\n        throw new Error(`Business scraper script not found`);\n      }\n      console.log(`Found business scraper script at alternative path: ${altScriptPath}`);\n      // Use the alternative path\n      scriptPath = altScriptPath;\n    }\n\n    console.log(`Using business scraper script at: ${scriptPath}`);\n    console.log(`Running with parameters: location=${input.location}, search term=${input.category}, country=${input.country}`);\n\n    // Prepare the command to run the Python script\n    const command = `python \"${scriptPath}\" --location \"${input.location}\" --category \"${input.category}\" --country \"${input.country}\"`;\n\n    // Execute the command\n    console.log(`Executing command: ${command}`);\n    const { stdout, stderr } = await execPromise(command);\n\n    if (stderr && !stderr.includes('DevTools listening')) {\n      console.error(`Python script error: ${stderr}`);\n      // Don't throw an error here, as some stderr output might be normal\n      // Only log it for debugging purposes\n    }\n\n    console.log(`Python script executed successfully`);\n\n    // Parse the output as JSON\n    try {\n      const results = JSON.parse(stdout);\n\n      // Ensure social_links field exists for all results\n      const processedResults = results.map((result: any) => ({\n        ...result,\n        social_links: result.social_links || \"N/A\"\n      }));\n\n      return processedResults;\n    } catch (parseError) {\n      console.error(`Error parsing JSON output: ${parseError}`);\n      console.error(`Raw output: ${stdout}`);\n      throw new Error(\"Failed to parse Python script output\");\n    }\n  } catch (error) {\n    console.error(\"Error running Python script:\", error);\n    // Re-throw the error instead of falling back to mock data\n    throw new Error(`Failed to scrape business data: ${error instanceof Error ? error.message : String(error)}`);\n  }\n};\n\nexport const scraperRouter = createTRPCRouter({\n  uploadCsv: publicProcedure\n    .input(\n      z.object({\n        fileName: z.string(),\n        fileContent: z.string(), // Base64 encoded file content\n      })\n    )\n    .mutation(async ({ input }) => {\n      try {\n        // Decode base64 content\n        const buffer = Buffer.from(input.fileContent, 'base64');\n        const csvContent = buffer.toString('utf-8');\n\n        // Parse CSV to extract URLs\n        const urls = parseCsv(csvContent);\n\n        if (urls.length === 0) {\n          throw new Error(\"No valid URLs found in the CSV file\");\n        }\n\n        // Process each URL (in a real app, this would be done in batches or with a queue)\n        const results = urls.map(url => mockScrapeUrl(url));\n\n        return results;\n      } catch (error) {\n        console.error(\"Error processing CSV:\", error);\n        throw new Error(\"Failed to process the CSV file\");\n      }\n    }),\n\n  scrapeBusiness: publicProcedure\n    .input(businessScrapeInputSchema)\n    .mutation(async ({ input }) => {\n      try {\n        // Call the Python script for business scraping\n        const results = await runBusinessScraperScript(input);\n\n        // Validate the results\n        return businessScrapeResultSchema.parse(results);\n      } catch (error) {\n        console.error(\"Error scraping business data:\", error);\n        throw new Error(\"Failed to scrape business data\");\n      }\n    }),\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,cAAc,CAAA,GAAA,iGAAA,CAAA,YAAS,AAAD,EAAE,mHAAA,CAAA,OAAI;AAElC,oDAAoD;AACpD,sEAAsE;AACtE,oEAAoE;AACpE,MAAM,gBAAgB,CAAC;IACrB,wCAAwC;IACxC,MAAM,SAAS,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;IAC/E,MAAM,cAAc,QAAQ,MAAM,QAAQ;QAAC;KAAU;IACrD,MAAM,eAAe,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK;IAEzG,4BAA4B;IAC5B,MAAM,SAAS;QAAC;QAAY;QAAe;QAAW;QAAW;QAAW;KAAe;IAC3F,MAAM,aAAa,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;IACpE,MAAM,qBAAqB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;IAC9D,MAAM,UAAU;QAAC;QAAW;QAAY;QAAY;QAAU;QAAa;KAAkB;IAC7F,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;IACxE,MAAM,UAAU,GAAG,mBAAmB,CAAC,EAAE,aAAa,EAAE,EAAE,WAAW,IAAI,CAAC;IAE1E,kCAAkC;IAClC,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,GAAG;IAC1K,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,GAAG;IAE1D,OAAO;QACL;QACA,MAAM;QACN;QACA;QACA;IACF;AACF;AAEA,4CAA4C;AAC5C,MAAM,WAAW,CAAC;IAChB,+CAA+C;IAC/C,MAAM,QAAQ,WAAW,KAAK,CAAC,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,OAAO;IAEvE,yDAAyD;IACzD,wEAAwE;IACxE,OAAO,MAAM,GAAG,CAAC,CAAA;QACf,gCAAgC;QAChC,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;QAC5B;QACA,uCAAuC;QACvC,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;IAC3B,GAAG,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC;AAClC;AAEA,mDAAmD;AACnD,MAAM,4BAA4B,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACzB,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACzB,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1B;AAEA,oDAAoD;AACpD,MAAM,6BAA6B,sIAAA,CAAA,IAAC,CAAC,KAAK,CACxC,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACP,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM;IACd,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM;IACjB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM;IACf,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM;IACf,KAAK,sIAAA,CAAA,IAAC,CAAC,MAAM;IACb,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACnC;AAKF,qFAAqF;AACrF,MAAM,2BAA2B,OAAO;IACtC,IAAI;QACF,iDAAiD;QACjD,IAAI,aAAa,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI;QAE7C,6BAA6B;QAC7B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;YAC9B,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,YAAY;YAClE,uBAAuB;YACvB,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,MAAM;YACxD,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,gBAAgB;gBACjC,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,cAAc,OAAO,CAAC;gBAC7F,MAAM,IAAI,MAAM,CAAC,iCAAiC,CAAC;YACrD;YACA,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,eAAe;YACjF,2BAA2B;YAC3B,aAAa;QACf;QAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,YAAY;QAC7D,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,MAAM,QAAQ,CAAC,cAAc,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE,MAAM,OAAO,EAAE;QAE1H,+CAA+C;QAC/C,MAAM,UAAU,CAAC,QAAQ,EAAE,WAAW,cAAc,EAAE,MAAM,QAAQ,CAAC,cAAc,EAAE,MAAM,QAAQ,CAAC,aAAa,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC;QAEnI,sBAAsB;QACtB,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS;QAC3C,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,YAAY;QAE7C,IAAI,UAAU,CAAC,OAAO,QAAQ,CAAC,uBAAuB;YACpD,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC9C,mEAAmE;QACnE,qCAAqC;QACvC;QAEA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,CAAC;QAEjD,2BAA2B;QAC3B,IAAI;YACF,MAAM,UAAU,KAAK,KAAK,CAAC;YAE3B,mDAAmD;YACnD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACrD,GAAG,MAAM;oBACT,cAAc,OAAO,YAAY,IAAI;gBACvC,CAAC;YAED,OAAO;QACT,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,YAAY;YACxD,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,QAAQ;YACrC,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,0DAA0D;QAC1D,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;IAC7G;AACF;AAEO,MAAM,gBAAgB,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5C,WAAW,8HAAA,CAAA,kBAAe,CACvB,KAAK,CACJ,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAED,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACxB,IAAI;YACF,wBAAwB;YACxB,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,WAAW,EAAE;YAC9C,MAAM,aAAa,OAAO,QAAQ,CAAC;YAEnC,4BAA4B;YAC5B,MAAM,OAAO,SAAS;YAEtB,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,MAAM,IAAI,MAAM;YAClB;YAEA,kFAAkF;YAClF,MAAM,UAAU,KAAK,GAAG,CAAC,CAAA,MAAO,cAAc;YAE9C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;IAEF,gBAAgB,8HAAA,CAAA,kBAAe,CAC5B,KAAK,CAAC,2BACN,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACxB,IAAI;YACF,+CAA+C;YAC/C,MAAM,UAAU,MAAM,yBAAyB;YAE/C,uBAAuB;YACvB,OAAO,2BAA2B,KAAK,CAAC;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM;QAClB;IACF;AACJ", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/api/root.ts"], "sourcesContent": ["import { postRouter } from \"~/server/api/routers/post\";\nimport { scraperRouter } from \"~/server/api/routers/scraper\";\nimport { createCallerFactory, createTRPCRouter } from \"~/server/api/trpc\";\n\n/**\n * This is the primary router for your server.\n *\n * All routers added in /api/routers should be manually added here.\n */\nexport const appRouter = createTRPCRouter({\n  post: postRouter,\n  scraper: scraperRouter,\n});\n\n// export type definition of API\nexport type AppRouter = typeof appRouter;\n\n/**\n * Create a server-side caller for the tRPC API.\n * @example\n * const trpc = createCaller(createContext);\n * const res = await trpc.post.all();\n *       ^? Post[]\n */\nexport const createCaller = createCallerFactory(appRouter);\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAOO,MAAM,YAAY,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,MAAM,yIAAA,CAAA,aAAU;IAChB,SAAS,4IAAA,CAAA,gBAAa;AACxB;AAYO,MAAM,eAAe,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/app/api/trpc/%5Btrpc%5D/route.ts"], "sourcesContent": ["import { fetchRe<PERSON><PERSON><PERSON><PERSON> } from \"@trpc/server/adapters/fetch\";\nimport { type NextRequest } from \"next/server\";\n\nimport { env } from \"~/env\";\nimport { appRouter } from \"~/server/api/root\";\nimport { createTRPCContext } from \"~/server/api/trpc\";\n\n/**\n * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when\n * handling a HTTP request (e.g. when you make requests from Client Components).\n */\nconst createContext = async (req: NextRequest) => {\n  return createTRPCContext({\n    headers: req.headers,\n  });\n};\n\nconst handler = (req: NextRequest) =>\n  fetchRequestHandler({\n    endpoint: \"/api/trpc\",\n    req,\n    router: appRouter,\n    createContext: () => createContext(req),\n    onError:\n      env.NODE_ENV === \"development\"\n        ? ({ path, error }) => {\n            console.error(\n              `❌ tRPC failed on ${path ?? \"<no-path>\"}: ${error.message}`,\n            );\n          }\n        : undefined,\n  });\n\nexport { handler as GET, handler as <PERSON><PERSON><PERSON> };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAGA;AACA;AACA;;;;;AAEA;;;CAGC,GACD,MAAM,gBAAgB,OAAO;IAC3B,OAAO,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB,SAAS,IAAI,OAAO;IACtB;AACF;AAEA,MAAM,UAAU,CAAC,MACf,CAAA,GAAA,uLAAA,CAAA,sBAAmB,AAAD,EAAE;QAClB,UAAU;QACV;QACA,QAAQ,8HAAA,CAAA,YAAS;QACjB,eAAe,IAAM,cAAc;QACnC,SACE,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBACb,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;YACd,QAAQ,KAAK,CACX,CAAC,iBAAiB,EAAE,QAAQ,YAAY,EAAE,EAAE,MAAM,OAAO,EAAE;QAE/D,IACA;IACR", "debugId": null}}]}