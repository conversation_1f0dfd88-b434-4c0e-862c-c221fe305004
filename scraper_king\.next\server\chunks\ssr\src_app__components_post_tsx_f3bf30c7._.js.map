{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/app/_components/post.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nimport { api } from \"~/trpc/react\";\n\nexport function LatestPost() {\n  const [latestPost] = api.post.getLatest.useSuspenseQuery();\n\n  const utils = api.useUtils();\n  const [name, setName] = useState(\"\");\n  const createPost = api.post.create.useMutation({\n    onSuccess: async () => {\n      await utils.post.invalidate();\n      setName(\"\");\n    },\n  });\n\n  return (\n    <div className=\"w-full max-w-xs\">\n      {latestPost ? (\n        <p className=\"truncate\">Your most recent post: {latestPost.name}</p>\n      ) : (\n        <p>You have no posts yet.</p>\n      )}\n      <form\n        onSubmit={(e) => {\n          e.preventDefault();\n          createPost.mutate({ name });\n        }}\n        className=\"flex flex-col gap-2\"\n      >\n        <input\n          type=\"text\"\n          placeholder=\"Title\"\n          value={name}\n          onChange={(e) => setName(e.target.value)}\n          className=\"w-full rounded-full bg-white/10 px-4 py-2 text-white\"\n        />\n        <button\n          type=\"submit\"\n          className=\"rounded-full bg-white/10 px-10 py-3 font-semibold transition hover:bg-white/20\"\n          disabled={createPost.isPending}\n        >\n          {createPost.isPending ? \"Submitting...\" : \"Submit\"}\n        </button>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAMO,SAAS;IACd,MAAM,CAAC,WAAW,GAAG,qHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB;IAExD,MAAM,QAAQ,qHAAA,CAAA,MAAG,CAAC,QAAQ;IAC1B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,aAAa,qHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QAC7C,WAAW;YACT,MAAM,MAAM,IAAI,CAAC,UAAU;YAC3B,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,2BACC,8OAAC;gBAAE,WAAU;;oBAAW;oBAAwB,WAAW,IAAI;;;;;;qCAE/D,8OAAC;0BAAE;;;;;;0BAEL,8OAAC;gBACC,UAAU,CAAC;oBACT,EAAE,cAAc;oBAChB,WAAW,MAAM,CAAC;wBAAE;oBAAK;gBAC3B;gBACA,WAAU;;kCAEV,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wBACvC,WAAU;;;;;;kCAEZ,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,UAAU,WAAW,SAAS;kCAE7B,WAAW,SAAS,GAAG,kBAAkB;;;;;;;;;;;;;;;;;;AAKpD", "debugId": null}}]}