{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"~/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"~/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className,\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className,\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/components/results/ScrapingResults.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useMemo } from \"react\"\nimport { But<PERSON> } from \"~/components/ui/button\"\nimport { Card, CardContent } from \"~/components/ui/card\"\n\nexport interface ScrapedResult {\n  name: string\n  address: string\n  phone: string\n  url: string\n  category?: string\n  social_links?: string\n  is_shopify: boolean\n  is_active: boolean\n  status?: string\n}\n\ninterface ScrapingResultsProps {\n  data: ScrapedResult[]\n  title: string\n}\n\ntype SortField = \"name\" | \"address\" | \"phone\" | \"url\" | \"social_links\" | \"is_shopify\" | \"is_active\"\ntype SortDirection = \"asc\" | \"desc\"\n\nexport default function ScrapingResults({ data, title }: ScrapingResultsProps) {\n  const [currentPage, setCurrentPage] = useState(1)\n  const [itemsPerPage] = useState(10)\n  const [sortField, setSortField] = useState<SortField>(\"name\")\n  const [sortDirection, setSortDirection] = useState<SortDirection>(\"asc\")\n\n  // Handle sorting\n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\")\n    } else {\n      setSortField(field)\n      setSortDirection(\"asc\")\n    }\n  }\n\n  // Sort and paginate data\n  const sortedData = useMemo(() => {\n    return [...data].sort((a, b) => {\n      let comparison = 0\n      \n      switch (sortField) {\n        case \"name\":\n          comparison = a.name.localeCompare(b.name)\n          break\n        case \"address\":\n          comparison = a.address.localeCompare(b.address)\n          break\n        case \"phone\":\n          comparison = a.phone.localeCompare(b.phone)\n          break\n        case \"url\":\n          comparison = a.url.localeCompare(b.url)\n          break\n        case \"social_links\":\n          comparison = (a.social_links || \"N/A\").localeCompare(b.social_links || \"N/A\")\n          break\n        case \"is_shopify\":\n          comparison = (a.is_shopify === b.is_shopify) ? 0 : a.is_shopify ? -1 : 1\n          break\n        case \"is_active\":\n          comparison = (a.is_active === b.is_active) ? 0 : a.is_active ? -1 : 1\n          break\n      }\n      \n      return sortDirection === \"asc\" ? comparison : -comparison\n    })\n  }, [data, sortField, sortDirection])\n\n  // Pagination\n  const totalPages = Math.ceil(sortedData.length / itemsPerPage)\n  const startIndex = (currentPage - 1) * itemsPerPage\n  const endIndex = startIndex + itemsPerPage\n  const currentData = sortedData.slice(startIndex, endIndex)\n\n  // Download as CSV\n  const downloadCsv = () => {\n    const headers = [\"Name\", \"Address\", \"Phone\", \"URL\", \"Category\", \"Social Links\", \"Shopify Site\", \"Active Domain\", \"Status\"]\n    const rows = sortedData.map(item => [\n      item.name,\n      item.address,\n      item.phone,\n      item.url,\n      item.category || \"\",\n      item.social_links || \"\",\n      item.is_shopify ? \"Yes\" : \"No\",\n      item.is_active ? \"Yes\" : \"No\",\n      item.status || \"\"\n    ])\n    \n    const csvContent = [\n      headers.join(','),\n      ...rows.map(row => row.map(cell => `\"${String(cell).replace(/\"/g, '\"\"')}\"`).join(','))\n    ].join('\\n')\n    \n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })\n    const url = URL.createObjectURL(blob)\n    const link = document.createElement('a')\n    link.setAttribute('href', url)\n    link.setAttribute('download', `${title.toLowerCase().replace(/\\s+/g, '_')}_results.csv`)\n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n  }\n\n  if (data.length === 0) {\n    return (\n      <Card className=\"bg-white/10 backdrop-blur-sm border-white/20\">\n        <CardContent className=\"p-8 text-center\">\n          <p className=\"text-white text-lg\">No results found. Try adjusting your search criteria.</p>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-white\">{title}</h2>\n          <p className=\"text-white/70\">Found {data.length} results</p>\n        </div>\n        <Button\n          onClick={downloadCsv}\n          className=\"bg-green-600 hover:bg-green-700 text-white\"\n        >\n          Download CSV\n        </Button>\n      </div>\n\n      {/* Results Table */}\n      <Card className=\"bg-white/10 backdrop-blur-sm border-white/20 overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full text-white\">\n            <thead>\n              <tr className=\"border-b border-white/10 bg-white/10 text-left\">\n                <th \n                  className=\"cursor-pointer p-4 font-semibold\"\n                  onClick={() => handleSort(\"name\")}\n                >\n                  Name {sortField === \"name\" && (sortDirection === \"asc\" ? \"↑\" : \"↓\")}\n                </th>\n                <th \n                  className=\"cursor-pointer p-4 font-semibold\"\n                  onClick={() => handleSort(\"address\")}\n                >\n                  Address {sortField === \"address\" && (sortDirection === \"asc\" ? \"↑\" : \"↓\")}\n                </th>\n                <th \n                  className=\"cursor-pointer p-4 font-semibold\"\n                  onClick={() => handleSort(\"phone\")}\n                >\n                  Phone {sortField === \"phone\" && (sortDirection === \"asc\" ? \"↑\" : \"↓\")}\n                </th>\n                <th \n                  className=\"cursor-pointer p-4 font-semibold\"\n                  onClick={() => handleSort(\"url\")}\n                >\n                  URL {sortField === \"url\" && (sortDirection === \"asc\" ? \"↑\" : \"↓\")}\n                </th>\n                <th \n                  className=\"cursor-pointer p-4 font-semibold\"\n                  onClick={() => handleSort(\"social_links\")}\n                >\n                  Social Links {sortField === \"social_links\" && (sortDirection === \"asc\" ? \"↑\" : \"↓\")}\n                </th>\n                <th \n                  className=\"cursor-pointer p-4 font-semibold\"\n                  onClick={() => handleSort(\"is_shopify\")}\n                >\n                  Shopify {sortField === \"is_shopify\" && (sortDirection === \"asc\" ? \"↑\" : \"↓\")}\n                </th>\n                <th \n                  className=\"cursor-pointer p-4 font-semibold\"\n                  onClick={() => handleSort(\"is_active\")}\n                >\n                  Active {sortField === \"is_active\" && (sortDirection === \"asc\" ? \"↑\" : \"↓\")}\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              {currentData.map((item, index) => (\n                <tr \n                  key={index} \n                  className=\"border-b border-white/10 transition hover:bg-white/5\"\n                >\n                  <td className=\"p-4\">{item.name}</td>\n                  <td className=\"p-4\">{item.address}</td>\n                  <td className=\"p-4\">{item.phone || \"N/A\"}</td>\n                  <td className=\"p-4\">\n                    <a\n                      href={item.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-blue-400 hover:underline\"\n                    >\n                      {item.url}\n                    </a>\n                  </td>\n                  <td className=\"p-4\">\n                    {item.social_links && item.social_links !== \"N/A\" ? (\n                      <div className=\"flex flex-col gap-1\">\n                        {item.social_links.split(\"; \").map((link, i) => (\n                          <a\n                            key={i}\n                            href={link}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-blue-400 hover:underline\"\n                          >\n                            {link.includes(\"facebook.com\") ? \"Facebook\" :\n                             link.includes(\"twitter.com\") ? \"Twitter\" :\n                             link.includes(\"instagram.com\") ? \"Instagram\" :\n                             link.includes(\"linkedin.com\") ? \"LinkedIn\" :\n                             link.includes(\"youtube.com\") ? \"YouTube\" :\n                             link.includes(\"pinterest.com\") ? \"Pinterest\" :\n                             link.includes(\"tiktok.com\") ? \"TikTok\" :\n                             link.includes(\"x.com\") ? \"X\" :\n                             new URL(link).hostname}\n                          </a>\n                        ))}\n                      </div>\n                    ) : (\n                      \"N/A\"\n                    )}\n                  </td>\n                  <td className=\"p-4\">\n                    <span className={item.is_shopify ? \"text-green-400\" : \"text-white/50\"}>\n                      {item.is_shopify ? \"Yes\" : \"No\"}\n                    </span>\n                  </td>\n                  <td className=\"p-4\">\n                    <span className={item.is_active ? \"text-green-400\" : \"text-white/50\"}>\n                      {item.is_active ? \"Yes\" : \"No\"}\n                    </span>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </Card>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"flex justify-center items-center gap-2\">\n          <Button\n            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\n            disabled={currentPage === 1}\n            variant=\"outline\"\n            className=\"text-white border-white/20\"\n          >\n            Previous\n          </Button>\n          \n          <span className=\"text-white px-4\">\n            Page {currentPage} of {totalPages}\n          </span>\n          \n          <Button\n            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}\n            disabled={currentPage === totalPages}\n            variant=\"outline\"\n            className=\"text-white border-white/20\"\n          >\n            Next\n          </Button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AA0Be,SAAS,gBAAgB,EAAE,IAAI,EAAE,KAAK,EAAwB;;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAChC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,iBAAiB;IACjB,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA,yBAAyB;IACzB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YACzB,OAAO;mBAAI;aAAK,CAAC,IAAI;uDAAC,CAAC,GAAG;oBACxB,IAAI,aAAa;oBAEjB,OAAQ;wBACN,KAAK;4BACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;4BACxC;wBACF,KAAK;4BACH,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,EAAE,OAAO;4BAC9C;wBACF,KAAK;4BACH,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;4BAC1C;wBACF,KAAK;4BACH,aAAa,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,GAAG;4BACtC;wBACF,KAAK;4BACH,aAAa,CAAC,EAAE,YAAY,IAAI,KAAK,EAAE,aAAa,CAAC,EAAE,YAAY,IAAI;4BACvE;wBACF,KAAK;4BACH,aAAa,AAAC,EAAE,UAAU,KAAK,EAAE,UAAU,GAAI,IAAI,EAAE,UAAU,GAAG,CAAC,IAAI;4BACvE;wBACF,KAAK;4BACH,aAAa,AAAC,EAAE,SAAS,KAAK,EAAE,SAAS,GAAI,IAAI,EAAE,SAAS,GAAG,CAAC,IAAI;4BACpE;oBACJ;oBAEA,OAAO,kBAAkB,QAAQ,aAAa,CAAC;gBACjD;;QACF;8CAAG;QAAC;QAAM;QAAW;KAAc;IAEnC,aAAa;IACb,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;IACjD,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAC9B,MAAM,cAAc,WAAW,KAAK,CAAC,YAAY;IAEjD,kBAAkB;IAClB,MAAM,cAAc;QAClB,MAAM,UAAU;YAAC;YAAQ;YAAW;YAAS;YAAO;YAAY;YAAgB;YAAgB;YAAiB;SAAS;QAC1H,MAAM,OAAO,WAAW,GAAG,CAAC,CAAA,OAAQ;gBAClC,KAAK,IAAI;gBACT,KAAK,OAAO;gBACZ,KAAK,KAAK;gBACV,KAAK,GAAG;gBACR,KAAK,QAAQ,IAAI;gBACjB,KAAK,YAAY,IAAI;gBACrB,KAAK,UAAU,GAAG,QAAQ;gBAC1B,KAAK,SAAS,GAAG,QAAQ;gBACzB,KAAK,MAAM,IAAI;aAChB;QAED,MAAM,aAAa;YACjB,QAAQ,IAAI,CAAC;eACV,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,OAAO,MAAM,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;SAClF,CAAC,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAA0B;QACtE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,YAAY,CAAC,QAAQ;QAC1B,KAAK,YAAY,CAAC,YAAY,GAAG,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,YAAY,CAAC;QACvF,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAE,WAAU;;oCAAgB;oCAAO,KAAK,MAAM;oCAAC;;;;;;;;;;;;;kCAElD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;0CACC,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,WAAW;;gDAC3B;gDACO,cAAc,UAAU,CAAC,kBAAkB,QAAQ,MAAM,GAAG;;;;;;;sDAEpE,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,WAAW;;gDAC3B;gDACU,cAAc,aAAa,CAAC,kBAAkB,QAAQ,MAAM,GAAG;;;;;;;sDAE1E,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,WAAW;;gDAC3B;gDACQ,cAAc,WAAW,CAAC,kBAAkB,QAAQ,MAAM,GAAG;;;;;;;sDAEtE,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,WAAW;;gDAC3B;gDACM,cAAc,SAAS,CAAC,kBAAkB,QAAQ,MAAM,GAAG;;;;;;;sDAElE,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,WAAW;;gDAC3B;gDACe,cAAc,kBAAkB,CAAC,kBAAkB,QAAQ,MAAM,GAAG;;;;;;;sDAEpF,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,WAAW;;gDAC3B;gDACU,cAAc,gBAAgB,CAAC,kBAAkB,QAAQ,MAAM,GAAG;;;;;;;sDAE7E,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,WAAW;;gDAC3B;gDACS,cAAc,eAAe,CAAC,kBAAkB,QAAQ,MAAM,GAAG;;;;;;;;;;;;;;;;;;0CAI/E,6LAAC;0CACE,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAG,WAAU;0DAAO,KAAK,IAAI;;;;;;0DAC9B,6LAAC;gDAAG,WAAU;0DAAO,KAAK,OAAO;;;;;;0DACjC,6LAAC;gDAAG,WAAU;0DAAO,KAAK,KAAK,IAAI;;;;;;0DACnC,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDACC,MAAM,KAAK,GAAG;oDACd,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAET,KAAK,GAAG;;;;;;;;;;;0DAGb,6LAAC;gDAAG,WAAU;0DACX,KAAK,YAAY,IAAI,KAAK,YAAY,KAAK,sBAC1C,6LAAC;oDAAI,WAAU;8DACZ,KAAK,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,kBACxC,6LAAC;4DAEC,MAAM;4DACN,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAET,KAAK,QAAQ,CAAC,kBAAkB,aAChC,KAAK,QAAQ,CAAC,iBAAiB,YAC/B,KAAK,QAAQ,CAAC,mBAAmB,cACjC,KAAK,QAAQ,CAAC,kBAAkB,aAChC,KAAK,QAAQ,CAAC,iBAAiB,YAC/B,KAAK,QAAQ,CAAC,mBAAmB,cACjC,KAAK,QAAQ,CAAC,gBAAgB,WAC9B,KAAK,QAAQ,CAAC,WAAW,MACzB,IAAI,IAAI,MAAM,QAAQ;2DAdlB;;;;;;;;;2DAmBX;;;;;;0DAGJ,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,KAAK,UAAU,GAAG,mBAAmB;8DACnD,KAAK,UAAU,GAAG,QAAQ;;;;;;;;;;;0DAG/B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,KAAK,SAAS,GAAG,mBAAmB;8DAClD,KAAK,SAAS,GAAG,QAAQ;;;;;;;;;;;;uCAlDzB;;;;;;;;;;;;;;;;;;;;;;;;;;YA6DhB,aAAa,mBACZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wBACxD,UAAU,gBAAgB;wBAC1B,SAAQ;wBACR,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAK,WAAU;;4BAAkB;4BAC1B;4BAAY;4BAAK;;;;;;;kCAGzB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,YAAY,cAAc;wBACjE,UAAU,gBAAgB;wBAC1B,SAAQ;wBACR,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;GA5PwB;KAAA", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/app/results/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSearchParams } from \"next/navigation\"\nimport { useEffect, useState } from \"react\"\nimport ScrapingResults, { type ScrapedResult } from \"~/components/results/ScrapingResults\"\nimport { But<PERSON> } from \"~/components/ui/button\"\nimport Link from \"next/link\"\n\nexport default function ResultsPage() {\n  const searchParams = useSearchParams()\n  const [results, setResults] = useState<ScrapedResult[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  const type = searchParams.get(\"type\") || \"data\"\n  const location = searchParams.get(\"location\") || \"\"\n  const category = searchParams.get(\"category\") || \"\"\n\n  useEffect(() => {\n    // In a real implementation, you would fetch the results from your backend\n    // For now, we'll simulate some data\n    const simulateResults = () => {\n      setLoading(true)\n      \n      // Simulate API call delay\n      setTimeout(() => {\n        const mockResults: ScrapedResult[] = [\n          {\n            name: \"Sample Business 1\",\n            address: \"123 Main St, Mumbai, Maharashtra\",\n            phone: \"+91 98765 43210\",\n            url: \"https://example1.com\",\n            category: category,\n            social_links: \"https://facebook.com/example1; https://instagram.com/example1\",\n            is_shopify: true,\n            is_active: true,\n            status: \"Active\"\n          },\n          {\n            name: \"Sample Business 2\",\n            address: \"456 Park Ave, Mumbai, Maharashtra\",\n            phone: \"+91 98765 43211\",\n            url: \"https://example2.com\",\n            category: category,\n            social_links: \"https://twitter.com/example2\",\n            is_shopify: false,\n            is_active: true,\n            status: \"Active\"\n          },\n          {\n            name: \"Sample Business 3\",\n            address: \"789 Business Rd, Mumbai, Maharashtra\",\n            phone: \"+91 98765 43212\",\n            url: \"https://example3.com\",\n            category: category,\n            social_links: \"N/A\",\n            is_shopify: false,\n            is_active: false,\n            status: \"Inactive\"\n          }\n        ]\n        \n        setResults(mockResults)\n        setLoading(false)\n      }, 2000)\n    }\n\n    simulateResults()\n  }, [location, category])\n\n  const getTitle = () => {\n    if (type === \"wine\") {\n      return `Wine Data Results for ${category}`\n    }\n    return `Business Data Results for ${category} in ${location}`\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-[hsl(280,100%,70%)] to-[hsl(240,100%,70%)] flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4\"></div>\n          <h2 className=\"text-2xl font-bold text-white mb-2\">Scraping Data...</h2>\n          <p className=\"text-white/70\">Please wait while we gather the information</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-[hsl(280,100%,70%)] to-[hsl(240,100%,70%)] flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-white mb-4\">Error</h2>\n          <p className=\"text-white/70 mb-6\">{error}</p>\n          <Link href=\"/\">\n            <Button className=\"bg-white/20 hover:bg-white/30 text-white\">\n              Go Back Home\n            </Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-[hsl(280,100%,70%)] to-[hsl(240,100%,70%)]\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <Link href=\"/\">\n            <Button variant=\"outline\" className=\"mb-4 text-white border-white/20 hover:bg-white/10\">\n              ← Back to Home\n            </Button>\n          </Link>\n        </div>\n\n        {/* Results */}\n        <ScrapingResults data={results} title={getTitle()} />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;IACzC,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;IACjD,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,0EAA0E;YAC1E,oCAAoC;YACpC,MAAM;yDAAkB;oBACtB,WAAW;oBAEX,0BAA0B;oBAC1B;iEAAW;4BACT,MAAM,cAA+B;gCACnC;oCACE,MAAM;oCACN,SAAS;oCACT,OAAO;oCACP,KAAK;oCACL,UAAU;oCACV,cAAc;oCACd,YAAY;oCACZ,WAAW;oCACX,QAAQ;gCACV;gCACA;oCACE,MAAM;oCACN,SAAS;oCACT,OAAO;oCACP,KAAK;oCACL,UAAU;oCACV,cAAc;oCACd,YAAY;oCACZ,WAAW;oCACX,QAAQ;gCACV;gCACA;oCACE,MAAM;oCACN,SAAS;oCACT,OAAO;oCACP,KAAK;oCACL,UAAU;oCACV,cAAc;oCACd,YAAY;oCACZ,WAAW;oCACX,QAAQ;gCACV;6BACD;4BAED,WAAW;4BACX,WAAW;wBACb;gEAAG;gBACL;;YAEA;QACF;gCAAG;QAAC;QAAU;KAAS;IAEvB,MAAM,WAAW;QACf,IAAI,SAAS,QAAQ;YACnB,OAAO,CAAC,sBAAsB,EAAE,UAAU;QAC5C;QACA,OAAO,CAAC,0BAA0B,EAAE,SAAS,IAAI,EAAE,UAAU;IAC/D;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;sCAA2C;;;;;;;;;;;;;;;;;;;;;;IAOvE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,WAAU;sCAAoD;;;;;;;;;;;;;;;;8BAO5F,6LAAC,mJAAA,CAAA,UAAe;oBAAC,MAAM;oBAAS,OAAO;;;;;;;;;;;;;;;;;AAI/C;GAlHwB;;QACD,qIAAA,CAAA,kBAAe;;;KADd", "debugId": null}}]}