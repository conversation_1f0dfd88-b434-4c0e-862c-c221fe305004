"use client";

import { useState, useRef, type ChangeEvent, type Drag<PERSON>vent } from "react";
import * as flaskApi from "~/api/flask-api";
import type { ScrapedResult } from "./ResultsTable";

interface CsvUploaderProps {
  onResults: (results: ScrapedResult[]) => void;
  onError: (error: string) => void;
  onLoading: (isLoading: boolean) => void;
}

export function CsvUploader({ onResults, onError, onLoading }: CsvUploaderProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [useAdvancedUploader, setUseAdvancedUploader] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      if (!file) {
        onError("No file selected");
        return;
      }

      // More robust file type checking - only validate extension and MIME type, not filename
      const isCSV =
        file.type === "text/csv" ||
        file.name.toLowerCase().endsWith(".csv") ||
        file.type === "application/vnd.ms-excel" || // Some systems use this MIME type for CSV
        file.type === "application/csv";

      if (!isCSV) {
        onError("Please select a CSV file");
        return;
      }

      if (file.size === 0) {
        onError("The selected file is empty");
        return;
      }

      console.log("File selected:", file.name, "Type:", file.type, "Size:", file.size);
      setSelectedFile(file);
    }
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0];

      if (!file) {
        onError("No file selected");
        return;
      }

      // More robust file type checking - only validate extension and MIME type, not filename
      const isCSV =
        file.type === "text/csv" ||
        file.name.toLowerCase().endsWith(".csv") ||
        file.type === "application/vnd.ms-excel" || // Some systems use this MIME type for CSV
        file.type === "application/csv";

      if (!isCSV) {
        onError("Please select a CSV file");
        return;
      }

      if (file.size === 0) {
        onError("The selected file is empty");
        return;
      }

      console.log("File dropped:", file.name, "Type:", file.type, "Size:", file.size);
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      onError("Please select a CSV file first");
      return;
    }

    // Additional validation
    if (selectedFile.size === 0) {
      onError("The selected file is empty");
      return;
    }

    // Check file extension again
    if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
      onError("Please select a valid CSV file");
      return;
    }

    try {
      onLoading(true);
      onError("");

      console.log("Uploading CSV file:", selectedFile.name, "Size:", selectedFile.size);

      // Check if the uploadCsv function exists
      if (typeof flaskApi.uploadCsv !== 'function') {
        console.error("uploadCsv is not a function:", flaskApi);
        onError("Internal error: uploadCsv function not found");
        onLoading(false);
        return;
      }

      console.log(`Using ${useAdvancedUploader ? 'advanced' : 'standard'} CSV uploader`);
      
      try {
        // Try with a smaller file first to test if the server is working
        const testFile = new File(
          [selectedFile.size > 1024 ? await selectedFile.slice(0, 1024).text() : await selectedFile.text()], 
          selectedFile.name, 
          { type: selectedFile.type }
        );
        
        console.log("Testing with smaller file first:", testFile.size, "bytes");
        
        try {
          const results = await flaskApi.uploadCsv(selectedFile, useAdvancedUploader);
          console.log("CSV upload results:", results);

          if (results && results.length > 0) {
            onResults(results);
          } else {
            onError("No results found in the CSV file");
          }
        } catch (uploadError) {
          console.error("CSV upload error:", uploadError);
          
          // Display a more user-friendly error message
          const errorMessage = uploadError instanceof Error ? uploadError.message : "Unknown error";
          const userMessage = errorMessage.includes("traceback") 
            ? "Server error processing the CSV file. Please check the file format and try again."
            : errorMessage;
          
          onError(userMessage);
          
          // Log the full error details to the console
          if (uploadError instanceof Error) {
            console.error("Full error details:", uploadError);
          }
        }
      } catch (fileError) {
        console.error("Error preparing file:", fileError);
        onError(`Error preparing file: ${fileError instanceof Error ? fileError.message : "Unknown error"}`);
      }
    } catch (error) {
      console.error("CSV upload error:", error);
      // More detailed error logging
      if (error instanceof Error) {
        console.error("Error name:", error.name);
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }
      onError(`Error: ${error instanceof Error ? error.message : "Failed to upload CSV file"}`);
    } finally {
      onLoading(false);
    }
  };

  const handleTestUpload = async () => {
    if (!selectedFile) {
      onError("Please select a CSV file first");
      return;
    }

    try {
      onLoading(true);
      onError("");

      console.log("Testing CSV upload with file:", selectedFile.name);
      
      try {
        const testResult = await flaskApi.testUploadCsv(selectedFile);
        console.log("Test upload result:", testResult);
        onError(`Test successful! File info: ${JSON.stringify(testResult)}`);
      } catch (testError) {
        console.error("Test upload error:", testError);
        onError(`Test upload error: ${testError instanceof Error ? testError.message : String(testError)}`);
      }
    } catch (error) {
      console.error("Test error:", error);
      onError(`Error: ${error instanceof Error ? error.message : "Test failed"}`);
    } finally {
      onLoading(false);
    }
  };

  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleClearFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleHealthCheck = async () => {
    try {
      onLoading(true);
      onError("");

      console.log("Checking API health");
      
      try {
        const result = await flaskApi.checkApiHealth();
        console.log("Health check result:", result);
        onError(`API health check: ${JSON.stringify(result)}`);
      } catch (healthError) {
        console.error("Health check error:", healthError);
        onError(`Health check error: ${healthError instanceof Error ? healthError.message : "Unknown error"}`);
      }
    } catch (error) {
      console.error("Health check error:", error);
      onError(`Error: ${error instanceof Error ? error.message : "Health check failed"}`);
    } finally {
      onLoading(false);
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-2">
        <label className="font-medium">Upload CSV File</label>
        <div
          className={`flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition ${
            isDragging
              ? "border-[hsl(280,100%,70%)] bg-[hsl(280,100%,70%)]/10"
              : "border-white/30 hover:border-white/50"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="file"
            accept=".csv"
            onChange={handleFileChange}
            className="hidden"
            ref={fileInputRef}
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mb-2 h-10 w-10 text-white/70"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>
          <p className="mb-1 text-center text-lg font-medium">
            Drag & Drop your CSV file here
          </p>
          <p className="text-center text-sm text-white/70">
            or{" "}
            <button
              type="button"
              onClick={handleBrowseClick}
              className="text-[hsl(280,100%,80%)] hover:underline"
            >
              browse files
            </button>
          </p>
          {selectedFile && (
            <div className="mt-4 flex items-center gap-2 rounded-lg bg-white/10 px-4 py-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-green-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="flex-1 truncate">{selectedFile.name}</span>
              <button
                type="button"
                onClick={handleClearFile}
                className="text-white/70 hover:text-white"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          )}
        </div>
        <div className="flex flex-col gap-2">
          <p className="text-sm text-white/70">
            CSV must include columns: name, location, country.
            <a
              href="/example.csv"
              download
              className="ml-1 text-[hsl(280,100%,80%)] hover:underline"
            >
              Download example CSV
            </a>
          </p>

          <div className="flex items-center gap-2 mt-2">
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={useAdvancedUploader}
                onChange={(e) => setUseAdvancedUploader(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-white/20 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[hsl(280,100%,70%)]"></div>
              <span className="ml-3 text-sm font-medium text-white/70">
                Use Advanced CSV Uploader
              </span>
            </label>
            <div className="text-xs text-white/50 ml-2">
              {useAdvancedUploader
                ? "Uses csv_uploader.py for better extraction"
                : "Uses standard extraction"}
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <button
          type="button"
          onClick={handleUpload}
          disabled={!selectedFile}
          className={`rounded-lg px-6 py-3 font-semibold transition ${
            selectedFile
              ? "bg-[hsl(280,100%,70%)] text-white hover:bg-[hsl(280,100%,60%)]"
              : "bg-white/10 text-white/50 cursor-not-allowed"
          }`}
        >
          Upload & Process CSV
        </button>
        <button
          onClick={handleTestUpload}
          className="ml-2 rounded bg-gray-600 px-4 py-2 text-white hover:bg-gray-700"
          disabled={!selectedFile}
        >
          Test Upload
        </button>
        <button
          onClick={handleHealthCheck}
          className="ml-2 rounded bg-purple-600 px-4 py-2 text-white hover:bg-purple-700"
        >
          Check API
        </button>
      </div>

      <div className="mt-2 rounded-lg bg-white/5 p-4">
        <h3 className="mb-2 font-medium">CSV Format Example:</h3>
        <pre className="overflow-x-auto whitespace-pre-wrap text-sm text-white/70">
          name,location,country
          Starbucks,New York,us
          Apple Store,San Francisco,us
          Microsoft Office,Seattle,us
        </pre>
        <p className="mt-2 text-sm text-white/70">
          The CSV file should have the following columns:
        </p>
        <ul className="mt-1 list-disc pl-5 text-sm text-white/70">
          <li><strong>name</strong>: Business name or search term</li>
          <li><strong>location</strong>: City, address, or general location</li>
          <li><strong>country</strong>: Country code (e.g., "us", "uk", "in")</li>
          <li><strong>url</strong> (optional): Website URL if known</li>
        </ul>
        <p className="mt-2 text-sm text-white/70">
          The system will automatically detect column headers and supports various CSV formats, including comma, semicolon, and tab-separated files.
        </p>
      </div>
    </div>
  );
}






