{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_31c89cc1.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_31c89cc1-module__hy39Qq__className\",\n  \"variable\": \"geist_31c89cc1-module__hy39Qq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_31c89cc1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-geist-sans%22}],%22variableName%22:%22geist%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/trpc/react.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TRPCReactProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TRPCReactProvider() from the server but TRPCReactProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/trpc/react.tsx <module evaluation>\",\n    \"TRPCReactProvider\",\n);\nexport const api = registerClientReference(\n    function() { throw new Error(\"Attempted to call api() from the server but api is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/trpc/react.tsx <module evaluation>\",\n    \"api\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oDACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,oDACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/trpc/react.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TRPCReactProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TRPCReactProvider() from the server but TRPCReactProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/trpc/react.tsx\",\n    \"TRPCReactProvider\",\n);\nexport const api = registerClientReference(\n    function() { throw new Error(\"Attempted to call api() from the server but api is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/trpc/react.tsx\",\n    \"api\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,gCACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,gCACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/app/layout.tsx"], "sourcesContent": ["import \"~/styles/globals.css\";\n\nimport { type <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON> } from \"next/font/google\";\n\nimport { TRPCReactProvider } from \"~/trpc/react\";\n\nexport const metadata: Metadata = {\n  title: \"Nashira - From Daru to Dolce Vita\",\n  description: \"Discover the finest wines and spirits with Nashira's comprehensive alcohol data extraction platform. Search by country, state, city, and alcohol type.\",\n  icons: [{ rel: \"icon\", url: \"/favicon.ico\" }],\n};\n\nconst geist = Geist({\n  subsets: [\"latin\"],\n  variable: \"--font-geist-sans\",\n});\n\nexport default function RootLayout({\n  children,\n}: Readonly<{ children: React.ReactNode }>) {\n  return (\n    <html lang=\"en\" className={`${geist.variable}`}>\n      <body>\n        <TRPCReactProvider>{children}</TRPCReactProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAKA;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,OAAO;QAAC;YAAE,KAAK;YAAQ,KAAK;QAAe;KAAE;AAC/C;AAOe,SAAS,WAAW,EACjC,QAAQ,EACgC;IACxC,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,EAAE;kBAC5C,cAAA,8OAAC;sBACC,cAAA,8OAAC,qHAAA,CAAA,oBAAiB;0BAAE;;;;;;;;;;;;;;;;AAI5B", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}