{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/app/_components/post.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LatestPost = registerClientReference(\n    function() { throw new Error(\"Attempted to call LatestPost() from the server but LatestPost is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/_components/post.tsx <module evaluation>\",\n    \"LatestPost\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8DACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/app/_components/post.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LatestPost = registerClientReference(\n    function() { throw new Error(\"Attempted to call LatestPost() from the server but LatestPost is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/_components/post.tsx\",\n    \"LatestPost\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0CACA", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/env.js"], "sourcesContent": ["import { createEnv } from \"@t3-oss/env-nextjs\";\nimport { z } from \"zod\";\n\nexport const env = createEnv({\n  /**\n   * Specify your server-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars.\n   */\n  server: {\n    DATABASE_URL: z.string().url(),\n    NODE_ENV: z\n      .enum([\"development\", \"test\", \"production\"])\n      .default(\"development\"),\n    FLASK_BACKEND_URL: z.string().url().default(\"http://localhost:5000\"),\n  },\n\n  /**\n   * Specify your client-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\n   * `NEXT_PUBLIC_`.\n   */\n  client: {\n    // NEXT_PUBLIC_CLIENTVAR: z.string(),\n  },\n\n  /**\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\n   * middlewares) or client-side so we need to destruct manually.\n   */\n  runtimeEnv: {\n    DATABASE_URL: process.env.DATABASE_URL,\n    NODE_ENV: process.env.NODE_ENV,\n    FLASK_BACKEND_URL: process.env.FLASK_BACKEND_URL,\n    // NEXT_PUBLIC_CLIENTVAR: process.env.NEXT_PUBLIC_CLIENTVAR,\n  },\n  /**\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\n   * useful for Docker builds.\n   */\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\n  /**\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\n   * `SOME_VAR=''` will throw an error.\n   */\n  emptyStringAsUndefined: true,\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QAC5B,UAAU,oIAAA,CAAA,IAAC,CACR,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa,EAC1C,OAAO,CAAC;QACX,mBAAmB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IAC9C;IAEA;;;;GAIC,GACD,QAAQ;IAER;IAEA;;;GAGC,GACD,YAAY;QACV,cAAc,QAAQ,GAAG,CAAC,YAAY;QACtC,QAAQ;QACR,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;IAElD;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/db.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\nimport { env } from \"~/env\";\n\nconst createPrismaClient = () =>\n  new PrismaClient({\n    log:\n      env.NODE_ENV === \"development\" ? [\"query\", \"error\", \"warn\"] : [\"error\"],\n  });\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: ReturnType<typeof createPrismaClient> | undefined;\n};\n\nexport const db = globalForPrisma.prisma ?? createPrismaClient();\n\nif (env.NODE_ENV !== \"production\") globalForPrisma.prisma = db;\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEA,MAAM,qBAAqB,IACzB,IAAI,6HAAA,CAAA,eAAY,CAAC;QACf,KACE,0GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBAAgB;YAAC;YAAS;YAAS;SAAO,GAAG;YAAC;SAAQ;IAC3E;AAEF,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI;AAE5C,IAAI,0GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,cAAc,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/api/trpc.ts"], "sourcesContent": ["/**\n * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:\n * 1. You want to modify request context (see Part 1).\n * 2. You want to create a new middleware or type of procedure (see Part 3).\n *\n * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will\n * need to use are documented accordingly near the end.\n */\nimport { initTRPC } from \"@trpc/server\";\nimport superjson from \"superjson\";\nimport { ZodError } from \"zod\";\n\nimport { db } from \"~/server/db\";\n\n/**\n * 1. CONTEXT\n *\n * This section defines the \"contexts\" that are available in the backend API.\n *\n * These allow you to access things when processing a request, like the database, the session, etc.\n *\n * This helper generates the \"internals\" for a tRPC context. The API handler and RSC clients each\n * wrap this and provides the required context.\n *\n * @see https://trpc.io/docs/server/context\n */\nexport const createTRPCContext = async (opts: { headers: Headers }) => {\n  return {\n    db,\n    ...opts,\n  };\n};\n\n/**\n * 2. INITIALIZATION\n *\n * This is where the tRPC API is initialized, connecting the context and transformer. We also parse\n * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation\n * errors on the backend.\n */\nconst t = initTRPC.context<typeof createTRPCContext>().create({\n  transformer: superjson,\n  errorFormatter({ shape, error }) {\n    return {\n      ...shape,\n      data: {\n        ...shape.data,\n        zodError:\n          error.cause instanceof ZodError ? error.cause.flatten() : null,\n      },\n    };\n  },\n});\n\n/**\n * Create a server-side caller.\n *\n * @see https://trpc.io/docs/server/server-side-calls\n */\nexport const createCallerFactory = t.createCallerFactory;\n\n/**\n * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)\n *\n * These are the pieces you use to build your tRPC API. You should import these a lot in the\n * \"/src/server/api/routers\" directory.\n */\n\n/**\n * This is how you create new routers and sub-routers in your tRPC API.\n *\n * @see https://trpc.io/docs/router\n */\nexport const createTRPCRouter = t.router;\n\n/**\n * Middleware for timing procedure execution and adding an artificial delay in development.\n *\n * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating\n * network latency that would occur in production but not in local development.\n */\nconst timingMiddleware = t.middleware(async ({ next, path }) => {\n  const start = Date.now();\n\n  if (t._config.isDev) {\n    // artificial delay in dev\n    const waitMs = Math.floor(Math.random() * 400) + 100;\n    await new Promise((resolve) => setTimeout(resolve, waitMs));\n  }\n\n  const result = await next();\n\n  const end = Date.now();\n  console.log(`[TRPC] ${path} took ${end - start}ms to execute`);\n\n  return result;\n});\n\n/**\n * Public (unauthenticated) procedure\n *\n * This is the base piece you use to build new queries and mutations on your tRPC API. It does not\n * guarantee that a user querying is authorized, but you can still access user session data if they\n * are logged in.\n */\nexport const publicProcedure = t.procedure.use(timingMiddleware);\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;AACD;AAAA;AACA;AACA;AAEA;;;;;AAcO,MAAM,oBAAoB,OAAO;IACtC,OAAO;QACL,IAAA,mHAAA,CAAA,KAAE;QACF,GAAG,IAAI;IACT;AACF;AAEA;;;;;;CAMC,GACD,MAAM,IAAI,gMAAA,CAAA,WAAQ,CAAC,OAAO,GAA6B,MAAM,CAAC;IAC5D,aAAa,0IAAA,CAAA,UAAS;IACtB,gBAAe,EAAE,KAAK,EAAE,KAAK,EAAE;QAC7B,OAAO;YACL,GAAG,KAAK;YACR,MAAM;gBACJ,GAAG,MAAM,IAAI;gBACb,UACE,MAAM,KAAK,YAAY,oIAAA,CAAA,WAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,KAAK;YAC9D;QACF;IACF;AACF;AAOO,MAAM,sBAAsB,EAAE,mBAAmB;AAcjD,MAAM,mBAAmB,EAAE,MAAM;AAExC;;;;;CAKC,GACD,MAAM,mBAAmB,EAAE,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;IACzD,MAAM,QAAQ,KAAK,GAAG;IAEtB,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE;QACnB,0BAA0B;QAC1B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QACjD,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;IACrD;IAEA,MAAM,SAAS,MAAM;IAErB,MAAM,MAAM,KAAK,GAAG;IACpB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,MAAM,EAAE,MAAM,MAAM,aAAa,CAAC;IAE7D,OAAO;AACT;AASO,MAAM,kBAAkB,EAAE,SAAS,CAAC,GAAG,CAAC", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/api/routers/post.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nimport { createTR<PERSON><PERSON>outer, publicProcedure } from \"~/server/api/trpc\";\n\nexport const postRouter = createTRPCRouter({\n  hello: publicProcedure\n    .input(z.object({ text: z.string() }))\n    .query(({ input }) => {\n      return {\n        greeting: `Hello ${input.text}`,\n      };\n    }),\n\n  create: publicProcedure\n    .input(z.object({ name: z.string().min(1) }))\n    .mutation(async ({ ctx, input }) => {\n      return ctx.db.post.create({\n        data: {\n          name: input.name,\n        },\n      });\n    }),\n\n  getLatest: publicProcedure.query(async ({ ctx }) => {\n    const post = await ctx.db.post.findFirst({\n      orderBy: { createdAt: \"desc\" },\n    });\n\n    return post ?? null;\n  }),\n});\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEO,MAAM,aAAa,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,OAAO,4HAAA,CAAA,kBAAe,CACnB,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IAClC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE;QACf,OAAO;YACL,UAAU,CAAC,MAAM,EAAE,MAAM,IAAI,EAAE;QACjC;IACF;IAEF,QAAQ,4HAAA,CAAA,kBAAe,CACpB,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAAG,IACzC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,OAAO,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,MAAM;gBACJ,MAAM,MAAM,IAAI;YAClB;QACF;IACF;IAEF,WAAW,4HAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QAC7C,MAAM,OAAO,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,OAAO,QAAQ;IACjB;AACF", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/api/routers/scraper.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { createTR<PERSON><PERSON>outer, publicProcedure } from \"~/server/api/trpc\";\nimport { env } from \"~/env\";\n\n// Flask backend configuration\nconst FLASK_BACKEND_URL = env.FLASK_BACKEND_URL;\n\n// Mock function to simulate scraping data from URLs\n// In a real implementation, this would make HTTP requests to the URLs\n// and extract the required information using a library like cheerio\nconst mockScrapeUrl = (url: string) => {\n  // Generate a business name from the URL\n  const domain = url.replace(/^https?:\\/\\//, '').replace(/\\/$/, '').split('/')[0];\n  const domainParts = domain?.split('.') || ['example'];\n  const businessName = domainParts[0] ? domainParts[0].charAt(0).toUpperCase() + domainParts[0].slice(1) : 'Business';\n\n  // Generate a random address\n  const cities = [\"New York\", \"Los Angeles\", \"Chicago\", \"Houston\", \"Phoenix\", \"Philadelphia\"];\n  const randomCity = cities[Math.floor(Math.random() * cities.length)];\n  const randomStreetNumber = Math.floor(Math.random() * 1000) + 1;\n  const streets = [\"Main St\", \"Broadway\", \"Park Ave\", \"Oak St\", \"Maple Ave\", \"Washington Blvd\"];\n  const randomStreet = streets[Math.floor(Math.random() * streets.length)];\n  const address = `${randomStreetNumber} ${randomStreet}, ${randomCity}, NY`;\n\n  // Generate random phone and email\n  const phone = Math.random() > 0.2 ? `+1 (${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}` : '';\n  const email = Math.random() > 0.3 ? `contact@${domain}` : '';\n\n  return {\n    url,\n    name: businessName,\n    address,\n    phone,\n    email\n  };\n};\n\n// Parse CSV content (simple implementation)\nconst parseCsv = (csvContent: string): string[] => {\n  // Split by newlines and filter out empty lines\n  const lines = csvContent.split(/\\r?\\n/).filter(line => line.trim() !== '');\n\n  // Extract URLs (assuming the first column contains URLs)\n  // This is a simplified implementation - a real one would be more robust\n  return lines.map(line => {\n    // Handle quoted values properly\n    if (line.startsWith('\"')) {\n      const match = line.match(/\"([^\"]+)\"/);\n      return match ? match[1] : '';\n    }\n    // Otherwise just take the first column\n    return line.split(',')[0];\n  }).filter(url => url.startsWith('http'));\n};\n\n// Define the schema for the business scraper input\nconst businessScrapeInputSchema = z.object({\n  location: z.string().min(1),\n  category: z.string().min(1), // 'category' is used as a generic search term\n  country: z.string().min(1),\n});\n\n// Define the schema for the business scraper result (updated to match Flask backend)\nconst businessScrapeResultSchema = z.array(\n  z.object({\n    name: z.string(),\n    address: z.string(),\n    phone: z.string(),\n    url: z.string(),\n    category: z.string().optional(),\n    social_links: z.string().optional(),\n    is_shopify: z.boolean().optional(),\n    is_active: z.boolean().optional(),\n  })\n);\n\n\n\n// Function to call the Flask backend for business scraping\nconst callFlaskBackend = async (input: z.infer<typeof businessScrapeInputSchema>) => {\n  try {\n    console.log(`Calling Flask backend at: ${FLASK_BACKEND_URL}/api/scrape`);\n    console.log(`Request data:`, input);\n\n    const response = await fetch(`${FLASK_BACKEND_URL}/api/scrape`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        category: input.category,\n        location: input.location,\n        country: input.country,\n        maxResults: 5, // Default value\n        filterShopify: false,\n        filterActive: false,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error(`Flask backend error: ${response.status} ${response.statusText}`);\n      console.error(`Error response: ${errorText}`);\n      throw new Error(`Flask backend returned ${response.status}: ${errorText}`);\n    }\n\n    const results = await response.json();\n    console.log(`Flask backend returned ${results.length} results`);\n\n    return results;\n  } catch (error) {\n    console.error(\"Error calling Flask backend:\", error);\n\n    // Check if it's a network error (Flask backend not running)\n    if (error instanceof TypeError && error.message.includes('fetch')) {\n      throw new Error(\"Cannot connect to Flask backend. Make sure it's running on http://localhost:5000\");\n    }\n\n    throw new Error(`Failed to scrape business data: ${error instanceof Error ? error.message : String(error)}`);\n  }\n};\n\nexport const scraperRouter = createTRPCRouter({\n  uploadCsv: publicProcedure\n    .input(\n      z.object({\n        fileName: z.string(),\n        fileContent: z.string(), // Base64 encoded file content\n      })\n    )\n    .mutation(async ({ input }) => {\n      try {\n        console.log(`Processing CSV upload: ${input.fileName}`);\n\n        // Create FormData to send to Flask backend\n        const formData = new FormData();\n\n        // Convert base64 back to file\n        const buffer = Buffer.from(input.fileContent, 'base64');\n        const blob = new Blob([buffer], { type: 'text/csv' });\n        formData.append('file', blob, input.fileName);\n\n        // Call Flask backend CSV upload endpoint\n        const response = await fetch(`${FLASK_BACKEND_URL}/api/upload-csv-with-uploader`, {\n          method: 'POST',\n          body: formData,\n        });\n\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error(`Flask backend CSV error: ${response.status} ${response.statusText}`);\n          console.error(`Error response: ${errorText}`);\n          throw new Error(`Flask backend returned ${response.status}: ${errorText}`);\n        }\n\n        const results = await response.json();\n        console.log(`Flask backend returned ${results.length} CSV results`);\n\n        return results;\n      } catch (error) {\n        console.error(\"Error processing CSV:\", error);\n\n        // Check if it's a network error (Flask backend not running)\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n          throw new Error(\"Cannot connect to Flask backend. Make sure it's running on http://localhost:5000\");\n        }\n\n        throw new Error(\"Failed to process the CSV file\");\n      }\n    }),\n\n  scrapeBusiness: publicProcedure\n    .input(businessScrapeInputSchema)\n    .mutation(async ({ input }) => {\n      try {\n        // Call the Flask backend for business scraping\n        const results = await callFlaskBackend(input);\n\n        // Validate the results\n        return businessScrapeResultSchema.parse(results);\n      } catch (error) {\n        console.error(\"Error scraping business data:\", error);\n        throw new Error(\"Failed to scrape business data\");\n      }\n    }),\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,8BAA8B;AAC9B,MAAM,oBAAoB,0GAAA,CAAA,MAAG,CAAC,iBAAiB;AAE/C,oDAAoD;AACpD,sEAAsE;AACtE,oEAAoE;AACpE,MAAM,gBAAgB,CAAC;IACrB,wCAAwC;IACxC,MAAM,SAAS,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;IAC/E,MAAM,cAAc,QAAQ,MAAM,QAAQ;QAAC;KAAU;IACrD,MAAM,eAAe,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK;IAEzG,4BAA4B;IAC5B,MAAM,SAAS;QAAC;QAAY;QAAe;QAAW;QAAW;QAAW;KAAe;IAC3F,MAAM,aAAa,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;IACpE,MAAM,qBAAqB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;IAC9D,MAAM,UAAU;QAAC;QAAW;QAAY;QAAY;QAAU;QAAa;KAAkB;IAC7F,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;IACxE,MAAM,UAAU,GAAG,mBAAmB,CAAC,EAAE,aAAa,EAAE,EAAE,WAAW,IAAI,CAAC;IAE1E,kCAAkC;IAClC,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,GAAG;IAC1K,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,GAAG;IAE1D,OAAO;QACL;QACA,MAAM;QACN;QACA;QACA;IACF;AACF;AAEA,4CAA4C;AAC5C,MAAM,WAAW,CAAC;IAChB,+CAA+C;IAC/C,MAAM,QAAQ,WAAW,KAAK,CAAC,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,OAAO;IAEvE,yDAAyD;IACzD,wEAAwE;IACxE,OAAO,MAAM,GAAG,CAAC,CAAA;QACf,gCAAgC;QAChC,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;QAC5B;QACA,uCAAuC;QACvC,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;IAC3B,GAAG,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC;AAClC;AAEA,mDAAmD;AACnD,MAAM,4BAA4B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACzB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACzB,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1B;AAEA,qFAAqF;AACrF,MAAM,6BAA6B,oIAAA,CAAA,IAAC,CAAC,KAAK,CACxC,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACP,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM;IACd,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM;IACjB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM;IACf,KAAK,oIAAA,CAAA,IAAC,CAAC,MAAM;IACb,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,YAAY,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAChC,WAAW,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;AACjC;AAKF,2DAA2D;AAC3D,MAAM,mBAAmB,OAAO;IAC9B,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,kBAAkB,WAAW,CAAC;QACvE,QAAQ,GAAG,CAAC,CAAC,aAAa,CAAC,EAAE;QAE7B,MAAM,WAAW,MAAM,MAAM,GAAG,kBAAkB,WAAW,CAAC,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,UAAU,MAAM,QAAQ;gBACxB,UAAU,MAAM,QAAQ;gBACxB,SAAS,MAAM,OAAO;gBACtB,YAAY;gBACZ,eAAe;gBACf,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC9E,QAAQ,KAAK,CAAC,CAAC,gBAAgB,EAAE,WAAW;YAC5C,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;QAC3E;QAEA,MAAM,UAAU,MAAM,SAAS,IAAI;QACnC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;QAE9D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,4DAA4D;QAC5D,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YACjE,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;IAC7G;AACF;AAEO,MAAM,gBAAgB,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5C,WAAW,4HAAA,CAAA,kBAAe,CACvB,KAAK,CACJ,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM;QAClB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM;IACvB,IAED,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACxB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM,QAAQ,EAAE;YAEtD,2CAA2C;YAC3C,MAAM,WAAW,IAAI;YAErB,8BAA8B;YAC9B,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,WAAW,EAAE;YAC9C,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAO,EAAE;gBAAE,MAAM;YAAW;YACnD,SAAS,MAAM,CAAC,QAAQ,MAAM,MAAM,QAAQ;YAE5C,yCAAyC;YACzC,MAAM,WAAW,MAAM,MAAM,GAAG,kBAAkB,6BAA6B,CAAC,EAAE;gBAChF,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;gBAClF,QAAQ,KAAK,CAAC,CAAC,gBAAgB,EAAE,WAAW;gBAC5C,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YAC3E;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YACnC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,MAAM,CAAC,YAAY,CAAC;YAElE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,4DAA4D;YAC5D,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;gBACjE,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,IAAI,MAAM;QAClB;IACF;IAEF,gBAAgB,4HAAA,CAAA,kBAAe,CAC5B,KAAK,CAAC,2BACN,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE;QACxB,IAAI;YACF,+CAA+C;YAC/C,MAAM,UAAU,MAAM,iBAAiB;YAEvC,uBAAuB;YACvB,OAAO,2BAA2B,KAAK,CAAC;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM;QAClB;IACF;AACJ", "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/server/api/root.ts"], "sourcesContent": ["import { postRouter } from \"~/server/api/routers/post\";\nimport { scraperRouter } from \"~/server/api/routers/scraper\";\nimport { createCallerFactory, createTRPCRouter } from \"~/server/api/trpc\";\n\n/**\n * This is the primary router for your server.\n *\n * All routers added in /api/routers should be manually added here.\n */\nexport const appRouter = createTRPCRouter({\n  post: postRouter,\n  scraper: scraperRouter,\n});\n\n// export type definition of API\nexport type AppRouter = typeof appRouter;\n\n/**\n * Create a server-side caller for the tRPC API.\n * @example\n * const trpc = createCaller(createContext);\n * const res = await trpc.post.all();\n *       ^? Post[]\n */\nexport const createCaller = createCallerFactory(appRouter);\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAOO,MAAM,YAAY,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,MAAM,uIAAA,CAAA,aAAU;IAChB,SAAS,0IAAA,CAAA,gBAAa;AACxB;AAYO,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/trpc/query-client.ts"], "sourcesContent": ["import {\n  defaultShouldDehydrateQuery,\n  QueryClient,\n} from \"@tanstack/react-query\";\nimport SuperJSON from \"superjson\";\n\nexport const createQueryClient = () =>\n  new QueryClient({\n    defaultOptions: {\n      queries: {\n        // With SSR, we usually want to set some default staleTime\n        // above 0 to avoid refetching immediately on the client\n        staleTime: 30 * 1000,\n      },\n      dehydrate: {\n        serializeData: SuperJSON.serialize,\n        shouldDehydrateQuery: (query) =>\n          defaultShouldDehydrateQuery(query) ||\n          query.state.status === \"pending\",\n      },\n      hydrate: {\n        deserializeData: SuperJSON.deserialize,\n      },\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAIA;;;AAEO,MAAM,oBAAoB,IAC/B,IAAI,6KAAA,CAAA,cAAW,CAAC;QACd,gBAAgB;YACd,SAAS;gBACP,0DAA0D;gBAC1D,wDAAwD;gBACxD,WAAW,KAAK;YAClB;YACA,WAAW;gBACT,eAAe,0IAAA,CAAA,UAAS,CAAC,SAAS;gBAClC,sBAAsB,CAAC,QACrB,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD,EAAE,UAC5B,MAAM,KAAK,CAAC,MAAM,KAAK;YAC3B;YACA,SAAS;gBACP,iBAAiB,0IAAA,CAAA,UAAS,CAAC,WAAW;YACxC;QACF;IACF", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/trpc/server.ts"], "sourcesContent": ["import \"server-only\";\n\nimport { createHydrationHelpers } from \"@trpc/react-query/rsc\";\nimport { headers } from \"next/headers\";\nimport { cache } from \"react\";\n\nimport { createCaller, type AppRouter } from \"~/server/api/root\";\nimport { createTRPCContext } from \"~/server/api/trpc\";\nimport { createQueryClient } from \"./query-client\";\n\n/**\n * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when\n * handling a tRPC call from a React Server Component.\n */\nconst createContext = cache(async () => {\n  const heads = new Headers(await headers());\n  heads.set(\"x-trpc-source\", \"rsc\");\n\n  return createTRPCContext({\n    headers: heads,\n  });\n});\n\nconst getQueryClient = cache(createQueryClient);\nconst caller = createCaller(createContext);\n\nexport const { trpc: api, HydrateClient } = createHydrationHelpers<AppRouter>(\n  caller,\n  getQueryClient,\n);\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;AAEA;;;CAGC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IAC1B,MAAM,QAAQ,IAAI,QAAQ,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACtC,MAAM,GAAG,CAAC,iBAAiB;IAE3B,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB,SAAS;IACX;AACF;AAEA,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,8HAAA,CAAA,oBAAiB;AAC9C,MAAM,SAAS,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;AAErB,MAAM,EAAE,MAAM,GAAG,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,yBAAsB,AAAD,EAC/D,QACA", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/abcdwer/scraper_king/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\n\nimport { LatestPost } from \"~/app/_components/post\";\nimport { api, HydrateClient } from \"~/trpc/server\";\n\nexport default async function Home() {\n  const hello = await api.post.hello({ text: \"from tRPC\" });\n\n  void api.post.getLatest.prefetch();\n\n  return (\n    <HydrateClient>\n      <main className=\"flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-[#2e026d] to-[#15162c] text-white\">\n        <div className=\"container flex flex-col items-center justify-center gap-12 px-4 py-16\">\n          <h1 className=\"text-5xl font-extrabold tracking-tight sm:text-[5rem]\">\n            Scraper <span className=\"text-[hsl(280,100%,70%)]\">King</span>\n          </h1>\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 md:gap-8\">\n            <Link\n              className=\"flex max-w-xs flex-col gap-4 rounded-xl bg-white/10 p-4 hover:bg-white/20\"\n              href=\"/scraper\"\n            >\n              <h3 className=\"text-2xl font-bold\">CSV Scraper →</h3>\n              <div className=\"text-lg\">\n                Upload a CSV file with URLs to extract business address, contact info, and email.\n              </div>\n            </Link>\n            <Link\n              className=\"flex max-w-xs flex-col gap-4 rounded-xl bg-white/10 p-4 hover:bg-white/20\"\n              href=\"/data-scraper\"\n            >\n              <h3 className=\"text-2xl font-bold\">Data Scraper →</h3>\n              <div className=\"text-lg\">\n                Professional data scraping platform with advanced filtering and analytics.\n              </div>\n            </Link>\n            <Link\n              className=\"flex max-w-xs flex-col gap-4 rounded-xl bg-white/10 p-4 hover:bg-white/20\"\n              href=\"/wine-scraper\"\n            >\n              <h3 className=\"text-2xl font-bold\">Wine Scraper →</h3>\n              <div className=\"text-lg\">\n                Extract comprehensive wine data, ratings, and pricing information.\n              </div>\n            </Link>\n          </div>\n          <div className=\"flex flex-col items-center gap-2\">\n            <p className=\"text-2xl text-white\">\n              {hello ? hello.greeting : \"Loading tRPC query...\"}\n            </p>\n          </div>\n\n          <LatestPost />\n        </div>\n      </main>\n    </HydrateClient>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;;AAEe,eAAe;IAC5B,MAAM,QAAQ,MAAM,qHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,KAAK,CAAC;QAAE,MAAM;IAAY;IAEvD,KAAK,qHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;IAEhC,qBACE,8OAAC,qHAAA,CAAA,gBAAa;kBACZ,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAwD;0CAC5D,8OAAC;gCAAK,WAAU;0CAA2B;;;;;;;;;;;;kCAErD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,WAAU;gCACV,MAAK;;kDAEL,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;kDAAU;;;;;;;;;;;;0CAI3B,8OAAC,4JAAA,CAAA,UAAI;gCACH,WAAU;gCACV,MAAK;;kDAEL,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;kDAAU;;;;;;;;;;;;0CAI3B,8OAAC,4JAAA,CAAA,UAAI;gCACH,WAAU;gCACV,MAAK;;kDAEL,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAI,WAAU;kDAAU;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCACV,QAAQ,MAAM,QAAQ,GAAG;;;;;;;;;;;kCAI9B,8OAAC,kIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;AAKrB", "debugId": null}}]}